#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSONL数据筛选和提取脚本
功能：从JSONL文件中提取包含"**推理结果**: **不"的数据，保存为JSON文件，并从原文件中删除
"""

import json
import os
import re
from datetime import datetime

def extract_and_filter_data(input_file):
    """
    从JSONL文件中提取包含特定关键字的数据
    
    Args:
        input_file: 输入JSONL文件路径
        output_file: 输出JSON文件路径（包含关键字的数据）
        backup_file: 备份文件路径（原始数据备份）
    """
    
    # 关键字模式
    keyword_pattern = r"\*\*推理结果\*\*: \*\*不"
    
    # 存储结果
    matched_data = []  # 包含关键字的数据
    remaining_data = []  # 不包含关键字的数据
    
    print(f"🔍 开始处理文件: {input_file}")
    print("-" * 50)
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误: 输入文件不存在 {input_file}")
        return
    
    try:
        # 读取JSONL文件
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        total_lines = len(lines)
        matched_count = 0
        
        print(f"📊 总数据条数: {total_lines}")
        
        # 逐行处理
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:  # 跳过空行
                continue
            
            try:
                # 解析JSON数据
                data = json.loads(line)
                
                # 检查output字段是否包含关键字
                output_text = data.get('output', '')
                
                if re.search(keyword_pattern, output_text):
                    # 包含关键字的数据
                    matched_data.append(data)
                    matched_count += 1
                    print(f"✅ 第 {line_num} 行: 找到匹配数据")
                    
                    # 显示匹配的部分内容（前100个字符）
                    match = re.search(keyword_pattern, output_text)
                    if match:
                        context_start = max(0, match.start() - 20)
                        context_end = min(len(output_text), match.end() + 20)
                        context = output_text[context_start:context_end]
                        print(f"   匹配内容: ...{context}...")
                else:
                    # 不包含关键字的数据
                    remaining_data.append(data)
                    
            except json.JSONDecodeError as e:
                print(f"⚠️  第 {line_num} 行: JSON解析错误 - {e}")
                # 解析错误的行保留在原文件中
                remaining_data.append({"error": f"JSON解析错误: {line}", "line_number": line_num})
        
        print(f"_matched_count: {matched_count}")
        
        # 更新原文件（删除匹配的数据）
        if remaining_data:
            with open(input_file, 'w', encoding='utf-8') as f:
                for data in remaining_data:
                    if "error" not in data:  # 跳过错误数据
                        f.write(json.dumps(data, ensure_ascii=False) + '\n')
            print(f"🔄 已更新原文件，保留 {len([d for d in remaining_data if 'error' not in d])} 条数据")
        else:
            # 如果所有数据都匹配，清空原文件
            with open(input_file, 'w', encoding='utf-8') as f:
                pass
            print("🗑️  所有数据都匹配，原文件已清空")
        
        # 输出统计结果
        print("\n" + "=" * 50)
        print("📊 处理完成统计:")
        print(f"总数据条数: {total_lines}")
        print(f"匹配数据条数: {matched_count}")
        print(f"剩余数据条数: {len([d for d in remaining_data if 'error' not in d])}")
        print(f"错误数据条数: {len([d for d in remaining_data if 'error' in d])}")
            
    except Exception as e:
        print(f"❌ 处理文件时出错: {e}")
        return

def main():
    """主函数"""
    # 设置文件路径
    input_file = "/Users/<USER>/Project/data/等红灯_extracted_data.jsonl"
    
    # 生成输出文件名（带时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    print("🔧 JSONL数据筛选和提取工具")
    print("=" * 50)
    print(f"📂 输入文件: {input_file}")
    print("=" * 50)
    
    # 执行提取和筛选
    extract_and_filter_data(input_file)

if __name__ == "__main__":
    main()
