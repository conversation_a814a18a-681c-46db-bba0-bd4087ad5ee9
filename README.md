# risk-center-python-script



## models


### extract.py

先使用sql去提取数据， 注意修改update_time的取值范围， 然后保存csv到本地

```sql
SELECT
  risk_case.*, case_mark_info.sub_category, risk_case_vehicle_relation.vin
from
  risk_case
  JOIN (
    SELECT
      *
    from
      case_mark_info
    WHERE
      update_time BETWEEN '2025-05-25'
      and '2025-06-01'
  ) as case_mark_info on case_mark_info.case_id = risk_case.case_id
  JOIN risk_case_vehicle_relation on risk_case_vehicle_relation.case_id = risk_case.case_id
WHERE
  sub_category in (
    'CONFLICT_WITH_PASSAGER',
    'RED_LIGHT',
    'STOP_BY_INACTIVE_VEHICLE',
    'IN_MIDDLE_ROAD',
    'OPPSITE_ROAD_DIRECTION',
    'WAITING_FRONT_PASSAGER',
    'IN_VALID_DRIVING_AREA',
    'STOP_BY_OBSTACLE'
  )
  and risk_case.update_time BETWEEN '2025-05-25'
  and '2025-06-01'
  and risk_case.source = 5 and risk_case.type = 1

```


替换这两个常量

- LABEL_PATH ： 保存的csv本地路径
- base_dir ： 数据集保存路径，注意新建一个images文件夹在里面



### requert_url.py

向大模型发送请求的脚本，便于快速验证准确率

### generate_pt.py 

生成压测数据，需要替换变量csv_path，和extract.py保持一致即可，预期会在执行代码的路径下产生一个2.csv。




