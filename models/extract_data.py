import requests
import os
import shutil
import matplotlib.pyplot as plt
import json
import pandas as pd
import time
from datetime import datetime, timedelta
from tqdm import tqdm
from copy import deepcopy
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading


def download_image(url, image_path, max_retries=None, timeout=None):
    """
    下载单张图片的函数
    """
    if max_retries is None:
        max_retries = MAX_RETRIES
    if timeout is None:
        timeout = DOWNLOAD_TIMEOUT

    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=timeout)
            response.raise_for_status()

            with open(image_path, "wb") as f:
                f.write(response.content)
            return True, image_path
        except Exception as e:
            if attempt == max_retries - 1:
                print(f"下载失败 {image_path}: {str(e)}")
                return False, image_path
            time.sleep(1)  # 重试前等待1秒
    return False, image_path


def download_images_parallel(download_tasks, max_workers=8):
    """
    并行下载图片
    download_tasks: [(url, image_path, image_name), ...]
    """
    results = {}
    failed_downloads = []

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有下载任务
        future_to_task = {}
        for url, image_path, image_name in download_tasks:
            future = executor.submit(download_image, url, image_path)
            future_to_task[future] = (url, image_path, image_name)

        # 使用tqdm显示进度
        with tqdm(total=len(download_tasks), desc="下载图片") as pbar:
            for future in as_completed(future_to_task):
                url, image_path, image_name = future_to_task[future]
                success, path = future.result()

                if success:
                    results[image_name] = path
                else:
                    failed_downloads.append((url, image_path, image_name))

                pbar.update(1)

    if failed_downloads:
        print(f"有 {len(failed_downloads)} 张图片下载失败")

    return results, failed_downloads


def extract_vehicle_info(vehicle_data_str):
    try:
        vehicle_data = json.loads(vehicle_data_str)
    except:
        return "# 目前已知车辆信息\n- 数据解析失败"

    # 映射字典定义
    drive_mode_map = {
        "UNKNOWN": "未知",
        "AUTONOMOUS_DRIVING": "自动驾驶",
        "CLOUD_SOFT_TAKEOVER": "云控软接管状态",
        "CLOUD_HARD_TAKEOVER": "云控硬接管状态",
        "MANUAL_CONTROL": "手动控制状态",
        "NO_CONTROL": "无控制状态"
    }

    traffic_light_map = {
        "RED": "红",
        "GREEN": "绿",
        "YELLOW": "黄",
        "NONE": "无红绿灯"
    }

    traffic_line_map = {
        "YELLOW_SOLID_LINE": "在黄线上行驶",
        "WHITE_SOLID_LINE": "在白线上行驶",
        "NONE": "没有在交通线行驶"
    }

    vehicle_type_length_map = {
        "H24": "2m",
        "S20": "2.5m"
    }

    lane_section_map = {
        "left": "车辆位于左边车道",
        "middle": "车辆位于中间车道",
        "right": "车辆位于右边车道"
    }

    # 规则1: 车辆驾驶状态
    drive_mode_raw = vehicle_data.get("driveMode")
    drive_mode = drive_mode_map.get(drive_mode_raw, "（未知，暂不考虑）") if drive_mode_raw else "（未知，暂不考虑）"

    # 规则2: 车辆行驶速度
    speed = vehicle_data.get("speed")
    if speed is None:
        speed = "（未知，暂不考虑）"

    # 规则3: 红绿灯状态
    traffic_light_raw = vehicle_data.get("trafficLightType")
    traffic_light = traffic_light_map.get(traffic_light_raw, "（未知，暂不考虑）") if traffic_light_raw else "（未知，暂不考虑）"

    # 获取红绿灯持续时间
    green_counter = vehicle_data.get("vehicleCounterInfo", {}).get("greenLightCounter", {}).get("duration")
    red_counter = vehicle_data.get("vehicleCounterInfo", {}).get("redLightCounter", {}).get("duration")

    if traffic_light == "绿" and green_counter is not None:
        light_duration = green_counter
        traffic_light_desc = f"前方{traffic_light}灯，持续{light_duration}秒"
    elif traffic_light == "红" and red_counter is not None:
        light_duration = red_counter
        traffic_light_desc = f"前方{traffic_light}灯，持续{light_duration}秒"
    elif traffic_light == "无红绿灯":
        traffic_light_desc = "前方无红绿灯"
    else:
        traffic_light_desc = "（未知，暂不考虑）"

    # 规则4: 前方路口距离
    distance_to_junction = vehicle_data.get("distanceToJunction")
    if distance_to_junction == -1: 
        distance_to_junction = "前方无路口"
    elif distance_to_junction is None:
        distance_to_junction = "（未知，暂不考虑）"
    else:
        distance_to_junction = f"{distance_to_junction}m"

    # 规则5: 车辆行驶方向
    opposite_with_road = vehicle_data.get("oppositeWithRoad")
    if opposite_with_road is None:
        direction = "（未知，暂不考虑）"
    else:
        direction = "逆行" if opposite_with_road else "正常行驶"

    # 规则6: 车辆在交通线上行驶情况
    traffic_line_raw = vehicle_data.get("drivingOnTrafficLineType")
    traffic_line = traffic_line_map.get(traffic_line_raw, "（未知，暂不考虑）") if traffic_line_raw else "（未知，暂不考虑）"

    # 规则7: 车辆停滞时间
    stagnation_duration = vehicle_data.get("vehicleCounterInfo", {}).get("stagnationCounter", {}).get("duration")
    if stagnation_duration is None:
        stagnation_duration = "（未知，暂不考虑）"

    # 规则8: 距离施工区域的距离
    construction_distance = vehicle_data.get("distanceToFrontConstructionZone")
    if construction_distance == -1:
        construction_desc = "附近没有施工区域"
    elif construction_distance is not None:
        construction_desc = f"{construction_distance}m"
    else:
        construction_desc = "（未知，暂不考虑）"

    # 规则9: 车辆类型
    vehicle_type = vehicle_data.get("vehicleType")
    if vehicle_type:
        vehicle_length = vehicle_type_length_map.get(vehicle_type, "（未知，暂不考虑）")
    else:
        vehicle_type = "（未知，暂不考虑）"
        vehicle_length = "（未知，暂不考虑）"


    # 规则11: 是否有可借车道
    usable_lane_ids = vehicle_data.get("usableLaneIds")
    if usable_lane_ids is None:
        has_usable_lane = "（未知，暂不考虑）"
    else:
        has_usable_lane = "有" if usable_lane_ids else "无"

    # 规则12: 是否在单车道
    single_lane = vehicle_data.get("singleLane")
    if single_lane is None:
        is_single_lane = "（未知，暂不考虑）"
    else:
        is_single_lane = "是" if single_lane else "否"

    # 规则13: 车辆在车道的位置
    vehicle_position_raw = vehicle_data.get("vehicleLaneSectionType")
    vehicle_position = lane_section_map.get(vehicle_position_raw, "（未知，暂不考虑）") if vehicle_position_raw else "（未知，暂不考虑）"

    # 规则14: 车辆距离左右路边距离
    distance_to_curb_list = vehicle_data.get("distanceToNearCurbList")
    if distance_to_curb_list and len(distance_to_curb_list) >= 2:
        left_distance = distance_to_curb_list[0]
        right_distance = distance_to_curb_list[1]
        curb_distance_desc = f"距离左边{left_distance:.1f}m，距离右边{right_distance:.1f}m"
    else:
        curb_distance_desc = "（未知，暂不考虑）"

    # 规则15: 车辆周围车道信息
    around_lane = vehicle_data.get("vehicleAroundLaneId")
    if around_lane:
        successor = "有车道" if around_lane.get("successor") else "无车道"
        left = "有车道" if around_lane.get("left") else "无车道"
        right = "有车道" if around_lane.get("right") else "无车道"
        predecessor = "有车道" if around_lane.get("predecessor") else "无车道"
        around_lane_desc = f"车辆前方{successor}，左边{left}，后方{predecessor}，右边{right}"
    else:
        around_lane_desc = "（未知，暂不考虑）"

    # 规则16: 车辆前1秒距离现在的距离
    pre_vehicle_distance = vehicle_data.get("preVehiclePosition", {}).get("distance")
    if pre_vehicle_distance is None:
        pre_vehicle_distance = "（未知，暂不考虑）"

    # 规则18: 车辆周围障碍物信息
    all_obstacles = vehicle_data.get("allObstacleList", [])
    total_obstacles = len(all_obstacles)

    # 分析障碍物
    front_barriers = []
    left_vehicles = []
    right_vehicles = []

    for obstacle in all_obstacles:
        # 路障处理 - 从allObstacleList中提取前方路障
        if obstacle.get("fineType") == "BARRIER" and obstacle.get("middleAngle", 0) < 60:
            front_barriers.append(obstacle)

        # 车辆处理 - 从allObstacleList中提取左右车辆
        if obstacle.get("type") == "VEHICLE" and "laneRelation2Vehicle" in obstacle:
            lane_relation = obstacle.get("laneRelation2Vehicle", "")
            if "left" in lane_relation:
                left_vehicles.append(obstacle)
            elif "right" in lane_relation:
                right_vehicles.append(obstacle)

    # 前方障碍物 - 使用frontObstacleList获取前方其他类型障碍物
    front_obstacles = vehicle_data.get("frontObstacleList", [])

    # 后方障碍物 - 使用behindObstacleList，按距离排序取最近的
    behind_obstacles = vehicle_data.get("behindObstacleList", [])
    if behind_obstacles:
        # 按距离排序，取最近的
        behind_nearest = min(behind_obstacles, key=lambda x: x.get("distance", float('inf')))
    else:
        behind_nearest = None

    # 构建障碍物描述
    if total_obstacles == 0:
        obstacle_parts = ["周围有（未知，暂不考虑）个障碍物"]
    else:
        obstacle_parts = [f"周围有{total_obstacles}个障碍物"]

    # 前方最近路障（从allObstacleList中的BARRIER类型）
    if front_barriers:
        nearest_barrier = min(front_barriers, key=lambda x: x.get("distance", float('inf')))
        obstacle_parts.append(f"前方最近路障为：路障距离{nearest_barrier.get('distance', 0):.1f}m")
    else:
        obstacle_parts.append("前方最近路障为：无")

    # 前方最近障碍物（从frontObstacleList中获取）
    if front_obstacles:
        nearest_front = min(front_obstacles, key=lambda x: x.get("distance", float('inf')))
        front_type = nearest_front.get('type', '未知')
        front_distance = nearest_front.get('distance', 0)
        obstacle_parts.append(f"前方最近障碍物为：{front_type}距离{front_distance:.1f}m")
    else:
        obstacle_parts.append("前方最近障碍物为：无")

    # 左边最近障碍物（从allObstacleList中的VEHICLE类型）
    if left_vehicles:
        nearest_left = min(left_vehicles, key=lambda x: x.get("distance", float('inf')))
        obstacle_parts.append(f"左边最近障碍物为：汽车距离{nearest_left.get('distance', 0):.1f}m")
    else:
        obstacle_parts.append("左边最近障碍物为：无")

    # 后方最近障碍物（从behindObstacleList，按距离排序）
    if behind_nearest:
        behind_type = behind_nearest.get('type', '未知')
        behind_distance = behind_nearest.get('distance', 0)
        obstacle_parts.append(f"后方最近障碍物为：{behind_type}距离{behind_distance:.1f}m")
    else:
        obstacle_parts.append("后方最近障碍物为：无")

    # 右边最近障碍物（从allObstacleList中的VEHICLE类型）
    if right_vehicles:
        nearest_right = min(right_vehicles, key=lambda x: x.get("distance", float('inf')))
        obstacle_parts.append(f"右边最近障碍物为：汽车距离{nearest_right.get('distance', 0):.1f}m")
    else:
        obstacle_parts.append("右边最近障碍物为：无")

    obstacle_desc = "，".join(obstacle_parts)

    # 生成完整描述
    vehicle_info = f"""# 目前已知车辆信息
- 车辆驾驶状态：{drive_mode}
- 车辆行驶速度：{speed}km/h
- 红绿灯状态：{traffic_light_desc}
- 前方路口距离：{distance_to_junction}
- 车辆行驶方向：{direction}
- 车辆在交通线上行驶情况：{traffic_line}
- 车辆停滞时间：{stagnation_duration}秒
- 距离施工区域的距离：{construction_desc}
- 是否有可借车道：{has_usable_lane}
- 是否在单车道：{is_single_lane}
- 车辆位置：{vehicle_position}
- 车辆距离左右路边距离：{curb_distance_desc}
- 车辆周围车道信息：{around_lane_desc}
- 车辆前1秒距离现在的距离：{pre_vehicle_distance}m
- 车辆周围障碍物信息：{obstacle_desc}
"""

    return vehicle_info


system_prompt = '''你是一个专业的自动驾驶安全评估系统，擅长准确辨别车辆是否处于风险或者正常的情况，尤其擅长辨别车辆停滞时刻的风险，并判断自动驾驶的车辆是否需要人工接管。
在此定义：
# 需要人工接管的场景：
- 位于施工区域、前方车辆上下客、前方车辆双闪故障、前方车辆熄火、前方车辆逆行等场景时，无法继续安全的、或者合理等待后通行
- 存在频繁、无序、恶意的车辆绕行行为，严重影响交通秩序和安全时（注：正常的变道、超车、避让行为不属于此类）
# 不需要人工接管的场景：
- 当本自动驾驶车辆正在等红灯，或者前方社会车辆等待红灯本自动车辆排在后方等待通行时
- 排队通行场景：车辆因交通信号灯、路口拥堵或前方车辆排队而正常停滞，周围车辆进行正常的、有序的变道或通行时（包括非机动车正常通行、其他车道车辆正常行驶、合理的变道行为等）
# 重要判断原则：
1. 优先识别交通场景类型（等红灯、排队通行、其他场景）
2. 在等红灯和排队通行场景中，其他车辆的正常变道、正常通行不应被视为需要接管的"绕行"行为
3. 只有当绕行行为频繁、无序且明显影响交通安全时，才需要人工接管
4. 重点评估整体交通秩序，而非单个车辆的移动行为
5. 区分车辆移动的目的性：正常交通需求（变道、避让、通行）vs 异常行为（恶意加塞、危险驾驶）'''

user_prompt_template = '''

请观看连续的自动驾驶车辆视频图片，最后一张图时刻本车处于停滞状态。
请你分析这些连续的图片，按照如下格式进行总结,[]内为推理条件，请使用，并替换成推理后的结果:
# 场景类型识别（优先分析）:
- 交通场景类型: [首先判断当前是否为等红灯场景（有明确红灯信号）、排队通行场景（因前方拥堵或车辆排队而停滞）、还是其他交通场景]
- 场景合理性评估: [评估当前停滞是否为正常的交通行为，是否符合交通规则]
- 整体交通秩序: [评估周围交通是否有序，车辆移动是否正常，是否存在明显异常]      
# 车辆行为:
- 是否正常行驶后减速:[从连续视频帧里面观察,本自动驾驶车辆是否缓慢减速至停滞]
- 是否倒行:[从连续视频帧里面观察，本自动驾驶车辆是否正在一点点后退]
- 是否缓慢蠕动行驶: [从连续视频帧里面观察，车辆是否在一点点在蠕动，有前进或者后退，并非带速行驶的速度]
# 道路特征：
- 车道环境: [从连续视频帧里面观察车辆所在的位置，是在中间、左侧，还是右侧车道]
- 车道类型: [是在机动车行驶的车道上，还是人车混行的车道上，还是在园区内部的道路，还是在施工区域的道路]
# 正前方车辆障碍物行为:
- 是否双闪：[从连续视频帧里面观察，尾灯是否频繁闪动、有规则的闪动并且为黄色，如果是则为双闪]
- 是否靠边熄火: [从连续视频帧里面观察，车辆是否处于右侧靠边并且没有尾灯亮起的状态，是则为熄火，注意尾灯高亮才算亮起]
- 是否工程车辆作业: [从连续视频帧里面观察，前方有大型工程车辆的作业部件正在运动，并且车辆周围有作业人员，正在工作]
- 是否上下客:[从连续视频帧里面观察，前方车辆正在有人上车或者下车]
- 是否正在装卸货:[从连续视频帧里面观察，前方车辆车厢打开，有人正在搬运货物]
# 周围车辆行为分析（基于场景类型）：
- 车辆移动性质判断:[基于已识别的场景类型，区分正常交通流动和异常绕行。在等红灯/排队通行场景中，重点关注是否存在频繁、无序、危险的绕行行为，而非正常的变道或通行]
- 绕行行为危险性评估:[如果存在车辆移动，评估其是否影响交通安全和秩序。在等红灯/排队通行场景中，其他车辆的正常变道、正常通行不应被视为危险的绕行行为]
- 是否有异常绕行:[从视频里面观察,是否有其他机动车或者非机动车进行频繁、无序、明显影响交通安全的绕行行为]
- 是否有对向行驶:[从视频里面观察，如果本自动驾驶车辆前方，有机动车不断向本车行驶，则算]
# 等红灯/排队通行场景特征确认:
- 是否为正常等待:[车辆停滞是否因红灯、路口拥堵或前方车辆排队等正常原因]
- 周围车辆行为合理性:[其他车辆的移动是否属于正常的交通流动，是否符合交通规则]
- 交通秩序评估:[整体交通环境是否有序，是否存在明显的异常情况]
**重要提醒**：
- 在等红灯和排队通行场景中，其他车辆的正常变道、正常通行是正常交通行为，不应被视为需要接管的异常情况
- 只有当车辆行为频繁、无序且明显影响交通安全时，才应被视为异常绕行
- 优先基于场景类型进行判断，避免机械地应用单一规则
{}
# 结论:
- 是否需要接管: [请你根据推理出来的信息和接管规则，告诉我是否需要接管本车辆,让人类介入以便继续通行。]
- 最终结论：[输出最终结论是否需要接管]'''


# ==================== 配置参数 ====================
LABEL_PATH = "/Users/<USER>/Project/data/models/data.csv"
base_dir = "/Users/<USER>/Project/data/models/dataTest"

# 并行处理配置
MAX_WORKERS = 16  # 并发下载线程数，可根据网络情况调整
DOWNLOAD_TIMEOUT = 30  # 下载超时时间（秒）
MAX_RETRIES = 3  # 下载重试次数

# 处理数据范围配置（可选，用于测试）
# 设置为None表示处理所有数据，设置为数字表示只处理前N条数据
PROCESS_LIMIT = 20 # 例如：设置为100表示只处理前100条数据



image_path_placeholders  = "<image_path_placeholder>"
target_placeholder  = "<target_placeholder>"

input_text_format = \
    {
    "input": [
        {
            "role" : "system", 
            "content" : [
                {
                    "type" : "text", 
                    "text" : system_prompt
                }
            ]
        },
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": ""
                }
            ]
        }
    ],
    "target": target_placeholder
}
    
take_over_list = [
    "车辆顶牛",
    "路中央停滞",
    "逆行停滞",
    "障碍物堵路",
    "施工区域停滞",
    "路边停车"
]


img_save_dir =  os.path.join(base_dir, "images")



label_df = pd.read_csv(LABEL_PATH)
url_template = "https://walle.sankuai.com/replay/video/avatarV2?vin={}&view=front&time={}"


map_dict = {
    "车辆顶牛"    : ["CONFLICT_WITH_PASSAGER", 0],
    "等红灯"      : ["RED_LIGHT", 0],
    "路中央停滞"   : ["IN_MIDDLE_ROAD", 0],
    "逆行停滞"     : ["OPPSITE_ROAD_DIRECTION", 0],
    "排队通行"     : ["WAITING_FRONT_PASSAGER", 0],
    "施工区域停滞"  : ["IN_VALID_DRIVING_AREA", 0],
    "障碍物堵路"    : ["STOP_BY_OBSTACLE", 0],
    "路边停车"     : ["STOP_ON_ROAD_SIDE", 0]
  }
map_dict = {v[0] : [k, 0] for (k, v) in map_dict.items()}

def process_data_parallel(max_workers=None):
    """
    并行处理数据的主函数
    """
    if max_workers is None:
        max_workers = MAX_WORKERS

    case_id_image_name = dict()
    all_input = ""
    count = 0

    # 确定处理范围
    total_rows = len(label_df)
    if PROCESS_LIMIT is not None:
        total_rows = min(PROCESS_LIMIT, total_rows)
        print(f"限制处理前 {total_rows} 条数据")

    # 第一步：准备所有需要下载的图片任务
    download_tasks = []
    valid_rows = []

    print("准备数据和下载任务...")
    for i in tqdm(range(total_rows), desc="准备任务"):
        image_cagtegory = label_df.loc[i, "sub_category"]
        vin = label_df.loc[i, "vin"]
        time_df = label_df.loc[i, "check_time"]
        case_id = label_df.loc[i, "case_id"]
        vehicle_runtime_info = label_df.loc[i, "vehicle_runtime_info_snapshot"]

        # 检查类别是否在映射字典中
        if image_cagtegory not in map_dict:
            print(f"跳过case_id {case_id}：未知类别 '{image_cagtegory}'")
            continue

        # 提取车辆信息
        vehicle_info = extract_vehicle_info(vehicle_runtime_info)

        # 检查车辆类型是否未知，如果未知则跳过这条数据
        if "车辆类型：（未知，暂不考虑）" in vehicle_info:
            print(f"跳过case_id {case_id}：车辆类型未知")
            continue

        if "车辆位置：（未知，暂不考虑）" in vehicle_info:
            print(f"跳过case_id {case_id}：车辆位置未知")
            continue

        # 保存有效的行数据
        valid_rows.append({
            'index': i,
            'image_cagtegory': image_cagtegory,
            'vin': vin,
            'time_df': time_df,
            'case_id': case_id,
            'vehicle_info': vehicle_info
        })

        # 准备下载任务
        for delta in range(0, 1):
            cur_time = datetime.strptime(time_df, "%Y-%m-%d %H:%M:%S") + timedelta(seconds=delta)
            dateKey = str(cur_time).replace(" ", "").replace(":", "").replace("-", "")
            url = f"https://walle.sankuai.com/replay/video/avatar?startTime={dateKey}&endTime={dateKey}&vin={vin}&view=front"
            image_name = f"{case_id}-{dateKey}.jpg"
            image_path = os.path.join(img_save_dir, image_name)

            download_tasks.append((url, image_path, image_name))
            case_id_image_name[image_name] = case_id

    print(f"准备了 {len(valid_rows)} 条有效数据，{len(download_tasks)} 个下载任务")

    # 第二步：并行下载所有图片
    print("开始并行下载图片...")
    downloaded_images, failed_downloads = download_images_parallel(download_tasks, max_workers)

    # 第三步：生成JSON数据
    print("生成JSON数据...")
    for row_data in tqdm(valid_rows, desc="生成数据"):
        i = row_data['index']
        image_cagtegory = row_data['image_cagtegory']
        case_id = row_data['case_id']
        vehicle_info = row_data['vehicle_info']
        time_df = row_data['time_df']

        # 生成包含车辆信息的user_prompt
        user_prompt = user_prompt_template.format(vehicle_info)

        cur_format = deepcopy(input_text_format)
        cur_format["input"][1]["content"][0]["text"] = user_prompt

        # 添加图片信息
        for delta in range(0, 1):
            cur_time = datetime.strptime(time_df, "%Y-%m-%d %H:%M:%S") + timedelta(seconds=delta)
            dateKey = str(cur_time).replace(" ", "").replace(":", "").replace("-", "")
            image_name = f"{case_id}-{dateKey}.jpg"

            # 检查图片是否下载成功
            if image_name in downloaded_images:
                cur_format["input"][1]["content"].append({
                    "type": "image_url",
                    "image_url": {
                        "url": "images/" + image_name
                    }
                })
            else:
                print(f"警告：图片 {image_name} 下载失败，跳过此数据")
                continue

        # 只有当所有图片都下载成功时才添加到结果中
        if len(cur_format["input"][1]["content"]) > 1:  # 除了text还有图片
            # 检查类别是否在映射字典中
            if image_cagtegory not in map_dict:
                print(f"警告：未知类别 '{image_cagtegory}'，跳过此数据")
                print(f"已知类别：{list(map_dict.keys())}")
                continue

            image_cagtegory_zh = map_dict[image_cagtegory][0]
            cur_input = json.dumps(cur_format, ensure_ascii=False)
            cur_input = cur_input.replace(target_placeholder, f"类型 : {image_cagtegory_zh}, 是否需要接管: {'是' if image_cagtegory_zh in take_over_list else '否'}  ")
            all_input = all_input + cur_input + "\n"
            count += 1

    print(f"成功处理了{count}条数据")
    with open(os.path.join(base_dir, "sample.json"), 'w', encoding="utf8") as f:
        f.write(all_input)

    return count, len(failed_downloads)


# 执行并行处理
if __name__ == "__main__":
    print("=" * 50)
    print("开始并行处理数据")
    print(f"配置参数：")
    print(f"  - 最大并发数: {MAX_WORKERS}")
    print(f"  - 下载超时: {DOWNLOAD_TIMEOUT}秒")
    print(f"  - 重试次数: {MAX_RETRIES}")
    print(f"  - 处理限制: {PROCESS_LIMIT if PROCESS_LIMIT else '全部数据'}")
    print("=" * 50)

    start_time = time.time()
    processed_count, failed_count = process_data_parallel()
    end_time = time.time()

    print("=" * 50)
    print("处理完成！")
    print(f"成功处理: {processed_count} 条数据")
    print(f"下载失败: {failed_count} 张图片")
    print(f"总耗时: {end_time - start_time:.2f} 秒")
    print("=" * 50)
# - 车辆类型：{vehicle_type}，车身{vehicle_length}